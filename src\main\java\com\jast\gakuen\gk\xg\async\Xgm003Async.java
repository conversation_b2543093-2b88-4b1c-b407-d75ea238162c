/*
 * Xgm003Async.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.async;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import com.jast.gakuen.core.common.SessionInfo;
import com.jast.gakuen.core.common.SystemInfo;
import com.jast.gakuen.core.common.UserInfo;
import com.jast.gakuen.core.common.annotation.RxAsyncErrorCheck;
import com.jast.gakuen.core.common.annotation.RxAsyncExecute;
import com.jast.gakuen.core.common.annotation.RxAsyncInit;
import com.jast.gakuen.core.common.annotation.RxAsyncNumberCount;
import com.jast.gakuen.core.gk.async.GkBaseCollectiveOutputAsync;
import com.jast.gakuen.core.common.constant.FileTypeConst;
import com.jast.gakuen.core.common.constant.code.PrdKbn;
import com.jast.gakuen.core.common.database.DbSession;
import com.jast.gakuen.core.common.dto.FileDTO;
import com.jast.gakuen.core.common.util.Message;
import com.jast.gakuen.core.common.util.RxLogger;
import com.jast.gakuen.core.common.util.Transporter;
import com.jast.gakuen.core.common.util.UtilDate;
import com.jast.gakuen.core.common.async.ICollectiveWriter;
import com.jast.gakuen.core.gk.util.UtilFormWriter;
import com.jast.gakuen.gk.xg.dto.Xgm003CollectiveOutputDTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO02;
import com.jast.gakuen.gk.xg.service.IXgm003Service;
import com.jast.gakuen.common.database.pk.dao.PkbGakHatbnDAO;
import com.jast.gakuen.common.database.pk.entity.PkbGakHatbnAR;
import com.jast.gakuen.gk.database.gh.dao.GhbGakDAO;
import com.jast.gakuen.gk.database.gh.entity.GhbGakAR;
import com.jast.gakuen.common.database.gh.dao.GhePaywBunDAO;
import com.jast.gakuen.common.database.gh.entity.GhePaywBunAR;
import com.jast.gakuen.core.common.exception.GakuenException;
import com.jast.gakuen.gk.gh.business.GhePayInfoStudentLogic;
import com.jast.gakuen.gk.gh.business.GhzGakSearchLogic;
import com.jast.gakuen.gk.gh.dto.GhePayInfoStudentConditionDTO03;
import com.jast.gakuen.gk.gh.dto.GhePayInfoStudentResultDTO01;
import com.jast.gakuen.gk.gh.dto.GhzGakSearchConditionDTO01;
import com.jast.gakuen.gk.gh.dto.GhzGakSearchResultDTO01;
import com.jast.gakuen.gk.gh.dto.GhzIndvDesignationGakseiDTO;

/**
 * 学生納付金通知書出力（CSV+PDF一括ダウンロード）非同期処理クラス
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
public class Xgm003Async extends GkBaseCollectiveOutputAsync {

	/**
	 * プロダクトコード
	 */
	public static final String PRD_CD = "Xg";

	/**
	 * 非同期処理ID
	 */
	public static final String ASYNC_EXEC_ID = "Xgm003Async";

	/**
	 * 処理名：CSV+PDF一括ダウンロード
	 */
	public static final String EXEC_PACKAGE_OUTPUT = "EXEC_PACKAGE_OUTPUT";

	/**
	 * ログ出力
	 */
	protected RxLogger LOGGER;

	/**
	 * ログインユーザ情報
	 */
	protected UserInfo userInfo;

	/**
	 * 学生納付金通知書サービス
	 */
	@Inject
	private IXgm003Service xgm003Service;

	/**
	 * 学生割当データ取得共通処理ロジック
	 */
	private GhePayInfoStudentLogic ghePayInfoStudentLogic;

	/**
	 * 学生(卒業生)検索共通処理ロジック
	 */
	private GhzGakSearchLogic ghzGakSearchLogic;

	/**
	 * Transporter
	 */
	private final Transporter transporter = new Transporter();

	/**
	 * コンストラクタ
	 *
	 * @throws Exception 例外
	 */
	public Xgm003Async() throws Exception {
		super();
		LOGGER = new RxLogger(Xgm003Async.class);
	}

	/**
	 * 初期処理
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	@RxAsyncInit(EXEC_PACKAGE_OUTPUT)
	public void init(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// ログインユーザ情報を取得
		SessionInfo sessionInfo = SessionInfo.getSessionInfo();
		this.userInfo = sessionInfo.getLoginUser();

		// 学生割当データ取得共通処理ロジックを初期化
		this.ghePayInfoStudentLogic = new GhePayInfoStudentLogic(dbs);

		// 学生(卒業生)検索共通処理ロジックを初期化
		this.ghzGakSearchLogic = new GhzGakSearchLogic(dbs, this.userInfo.getUserId());
	}

	/**
	 * エラーチェック
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return エラーチェック結果
	 * @throws Exception 例外
	 */
	@RxAsyncErrorCheck(EXEC_PACKAGE_OUTPUT)
	public List<Message> errorCheck(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		List<Message> messageList = new ArrayList<>();

		if (condition.getPayList().isEmpty()) {
			// E_SY_00058={0}は存在しません。
			messageList.add(new Message("SY", Message.TypeCode.E, 58, "納付金"));
		}

		return messageList;
	}

	/**
	 * 処理件数取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 処理件数
	 * @throws Exception 例外
	 */
	@RxAsyncNumberCount(EXEC_PACKAGE_OUTPUT)
	public int getCount(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// 処理件数は固定で2（CSV + PDF）
		return 2;
	}

	/**
	 * 業務処理：CSV+PDF一括ダウンロード
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	@RxAsyncExecute(EXEC_PACKAGE_OUTPUT)
	public void executePackageOutput(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {

		LOGGER.info("CSV+PDF一括ダウンロード処理開始");

		// 圧縮対象ファイルのリスト
		List<FileDTO> zipFileDtoList = new ArrayList<>();

		try {
			// 1. CSV ファイル生成
			FileDTO csvFileDto = generateCsvFile(dbs, condition);
			if (csvFileDto != null) {
				zipFileDtoList.add(csvFileDto);
				addNormalCount(1);
				addCount(1);
			}

			// 2. PDF ファイル生成
			FileDTO pdfFileDto = generatePdfFile(dbs, condition);
			if (pdfFileDto != null) {
				zipFileDtoList.add(pdfFileDto);
				addNormalCount(1);
				addCount(1);
			}

			// 3. ZIP ファイル生成
			if (!zipFileDtoList.isEmpty()) {
				FileDTO zipFileDTO = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_ZIP01", FileTypeConst.ZIP);
				// ZIPファイルを生成し、圧縮対象ファイルを登録する
				compressOutputFile(zipFileDTO, zipFileDtoList, true);
			}

		} catch (Exception e) {
			LOGGER.error(e);
			addErrorCount(1);
			throw e;
		}

		LOGGER.info("CSV+PDF一括ダウンロード処理終了");
	}

	/**
	 * CSV ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return CSV ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generateCsvFile(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		LOGGER.info("CSV ファイル生成開始");

		// CSV ファイルのFileDTOを取得
		FileDTO csvFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_CSV01", FileTypeConst.CSV);

		// 出力ファイル用の一括出力用ユーティリティを取得する
		try (ICollectiveWriter csvWriter = getCollectiveWriter(csvFileDto)) {

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// データ行を出力
			for (Xgm003DTO02 data : dataList) {
				writeCsvDataLine(dbs, csvWriter, data, condition);
				addNormalCount(1);
				addCount(1);
			}

			LOGGER.info("CSV ファイル生成完了：" + dataList.size() + "件");
		}

		LOGGER.info("CSV ファイル生成終了");
		return csvFileDto;
	}

	/**
	 * PDF ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return PDF ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generatePdfFile(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		LOGGER.info("PDF ファイル生成開始");

		// PDF ファイルのFileDTOを取得
		FileDTO pdfFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_PDF01", FileTypeConst.PDF);

		// SVF を使用してPDF生成
		UtilFormWriter pdfWriter = new UtilFormWriter();

		try {
			// PDF ファイルを開く
			pdfWriter.open(UtilFormWriter.DocFileType.PDF, pdfFileDto.getAbsolutePath());

			// SVF テンプレート（Xgm003RPT01.xml）を使用してPDF生成
			pdfWriter.setForm(com.jast.gakuen.core.common.constant.code.PrdKbn.XG, "Xgm003RPT01");

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// 各納付金データに対してPDFページを生成
			for (Xgm003DTO02 data : dataList) {
				// 基本情報を設定
				pdfWriter.print("年度", String.valueOf(data.getNendo()));
				pdfWriter.print("納付金コード", data.getPayCd());
				pdfWriter.print("納付金名称", data.getPayName());
				pdfWriter.print("分納区分", data.getBunnoKbnName());
				pdfWriter.print("分割番号", String.valueOf(data.getBunkatsuNo()));

				// 納入期限を設定
				if (data.getPayLimitDate() != null) {
					pdfWriter.print("納入期限", data.getPayLimitDate().toString());
				}

				// 発行日を設定
				if (condition.getHattyuDate() != null) {
					pdfWriter.print("発行日", condition.getHattyuDate().toString());
				}

				// 通信欄を設定
				if (condition.getTsuukyak() != null) {
					pdfWriter.print("通信欄", condition.getTsuukyak());
				}

				// 次のページに移動（最後のデータでない場合）
				pdfWriter.next();

				addNormalCount(1);
				addCount(1);
			}

			LOGGER.info("PDF ファイル生成完了：" + dataList.size() + "件");

		} finally {
			pdfWriter.close();
		}

		LOGGER.info("PDF ファイル生成終了");
		return pdfFileDto;
	}

	/**
	 * 納付金通知書データを取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 納付金通知書データリスト
	 * @throws Exception 例外
	 */
	private List<Xgm003DTO02> getNotificationData(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		List<Xgm003DTO02> resultList = new ArrayList<>();

		// 選択された納付金リストが存在する場合
		if (condition.getPayList() != null && !condition.getPayList().isEmpty()) {
			// 選択された納付金リストが存在する場合は、そのまま使用
			// 学生情報の補完は行わない（既に必要な情報が設定済みと仮定）
			resultList.addAll(condition.getPayList());
			return resultList;
		}

		// 選択された納付金リストが存在しない場合は、学生割当データ取得共通処理で取得
		// 学籍番号リストが空の場合は処理対象なし
		if (condition.getGaksekiCdList() == null || condition.getGaksekiCdList().isEmpty()) {
			LOGGER.warn("対象学籍番号リストが空のため、処理対象データなし");
			return resultList; // 空のリストを返す
		}

		// 学生割当データ取得共通処理を使用してデータを取得
		GhePayInfoStudentConditionDTO03 studentCondition = new GhePayInfoStudentConditionDTO03();
		studentCondition.setUserId(this.userInfo.getUserId());
		studentCondition.setCurrentDate(UtilDate.getCurrentTimeStamp());

		// 学籍番号リストをGhzIndvDesignationGakseiDTOリストに変換
		List<GhzIndvDesignationGakseiDTO> gakseiList = convertGaksekiCdListToGakseiList(condition.getGaksekiCdList());
		studentCondition.setGakseiList(gakseiList);

		List<GhePayInfoStudentResultDTO01> studentResultList = this.ghePayInfoStudentLogic.getPayInfoStudent(studentCondition);

		// データが存在しない場合はエラー
		if (studentResultList == null || studentResultList.isEmpty()) {
			throw new GakuenException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 199, "納付金"));
		}

		// 各学生割当データをXgm003DTO02に変換し、学生情報を補完
		for (GhePayInfoStudentResultDTO01 studentResult : studentResultList) {
			Xgm003DTO02 payData = convertToXgm003DTO02(studentResult);
			Xgm003DTO02 enrichedData = enrichPaymentDataWithStudentInfo(dbs, payData, studentResult, condition);
			resultList.add(enrichedData);
		}

		return resultList;
	}

	/**
	 * CSV データ行を出力
	 *
	 * @param dbs DBセッション
	 * @param csvWriter CSV ライター
	 * @param data 納付金データ
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	private void writeCsvDataLine(final DbSession dbs, final ICollectiveWriter csvWriter, final Xgm003DTO02 data, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// 21フィールド形式でCSV出力
		List<String> csvLine = new ArrayList<>();

		// 01: データ区分（固定値：2）
		csvLine.add("2");

		// 02: 処理区分（固定値：0）
		csvLine.add("0");

		// 03: 顧客番号（委託人コード）
		String furikomiIraiCd = getFurikomiIraiCd(dbs, data, condition);
		csvLine.add(furikomiIraiCd != null ? furikomiIraiCd : "");

		// 04: 確認番号
		csvLine.add(generateKakuninNo(data));

		// 05: コンビニネットコード
		csvLine.add(generateKonbiniNetCd(data));

		// 06: 請求金額
		csvLine.add("0");

		// 07: 元金
		csvLine.add("0");

		// 08: 延滞金額
		csvLine.add("0");

		// 09: 消費税
		csvLine.add("0");

		// 10: 請求内容カナ
		csvLine.add(data.getPayName() != null ? data.getPayName() : "");

		// 11: 請求内容漢字
		csvLine.add(data.getPayName() != null ? data.getPayName() : "");

		// 12: 氏名カナ
		csvLine.add("");

		// 13: 氏名漢字
		csvLine.add("");

		// 14-20: 空フィールド（7個）
		for (int i = 0; i < 7; i++) {
			csvLine.add("");
		}

		// 21: 請求情報有効期限
		csvLine.add(data.getPayLimitDate() != null ? data.getPayLimitDate().toString() : "");

		// CSV行を出力
		csvWriter.writeLineData(csvLine, false);
	}

	/**
	 * 学籍番号リストをGhzIndvDesignationGakseiDTOリストに変換
	 *
	 * @param gaksekiCdList 学籍番号リスト
	 * @return GhzIndvDesignationGakseiDTOリスト
	 */
	private List<GhzIndvDesignationGakseiDTO> convertGaksekiCdListToGakseiList(final List<String> gaksekiCdList) {
		List<GhzIndvDesignationGakseiDTO> gakseiList = new ArrayList<>();

		if (gaksekiCdList != null) {
			for (String gaksekiCd : gaksekiCdList) {
				GhzIndvDesignationGakseiDTO gaksei = new GhzIndvDesignationGakseiDTO();
				gaksei.setGaksekiCd(gaksekiCd);
				gakseiList.add(gaksei);
			}
		}

		return gakseiList;
	}

	/**
	 * GhePayInfoStudentResultDTO01をXgm003DTO02に変換
	 *
	 * @param studentResult 学生割当データ
	 * @return 納付金データ
	 */
	private Xgm003DTO02 convertToXgm003DTO02(final GhePayInfoStudentResultDTO01 studentResult) {
		Xgm003DTO02 payData = new Xgm003DTO02();

		// 基本情報を設定（Xgm003DTO02の実際のフィールドに合わせて設定）
		payData.setNendo(studentResult.getNendo() != null ? studentResult.getNendo() : 0);
		payData.setPayCd(studentResult.getPayCd());
		payData.setPatternCd(studentResult.getPatternCd());
		payData.setBunnoKbnCd(studentResult.getBunnoKbnCd() != null ? studentResult.getBunnoKbnCd() : 0);
		payData.setBunkatsuNo(studentResult.getBunkatsuNo() != null ? studentResult.getBunkatsuNo() : 0);

		return payData;
	}

	/**
	 * 納付金データに学生情報と金額情報を補完
	 *
	 * @param dbs DBセッション
	 * @param payData 納付金データ
	 * @param studentResult 学生割当データ
	 * @param condition 非同期処理DTO
	 * @return 補完された納付金データ
	 * @throws Exception 例外
	 */
	private Xgm003DTO02 enrichPaymentDataWithStudentInfo(final DbSession dbs, final Xgm003DTO02 payData, final GhePayInfoStudentResultDTO01 studentResult, final Xgm003CollectiveOutputDTO02 condition) throws Exception {

		// 学生情報を取得（ログ出力用）
		List<GhzGakSearchResultDTO01> gakInfoList = getGakInfoList(dbs, studentResult);

		if (gakInfoList != null && !gakInfoList.isEmpty()) {
			LOGGER.info("学生情報取得成功: 学籍番号=" + studentResult.getGaksekiCd() + ", 管理番号=" + studentResult.getKanriNo());
		} else {
			LOGGER.warn("学生情報取得失敗: 学籍番号=" + studentResult.getGaksekiCd() + ", 管理番号=" + studentResult.getKanriNo());
		}

		// Xgm003DTO02は基本的な納付金情報のみを持つため、学生詳細情報は設定しない
		// 必要に応じて将来的にXgm003DTO02にフィールドを追加することを検討

		return payData;
	}

	/**
	 * 学生情報リストを取得
	 *
	 * @param dbs DBセッション
	 * @param studentResult 学生割当データ
	 * @return 学生情報リスト
	 * @throws Exception 例外
	 */
	private List<GhzGakSearchResultDTO01> getGakInfoList(final DbSession dbs, final GhePayInfoStudentResultDTO01 studentResult) throws Exception {

		// 処理区分リストを設定
		List<String> shoriKbnList = new ArrayList<>();
		String productKbn = studentResult.getProductKbn();
		String gakSotKbn = studentResult.getGakSotKbn();

		if ("PK".equals(productKbn)) {
			if ("GAK".equals(gakSotKbn)) {
				shoriKbnList.add("1"); // 教務在学生
				shoriKbnList.add("2"); // 教務出学予定者
			} else if ("SOT".equals(gakSotKbn)) {
				shoriKbnList.add("5"); // 教務卒業生
				shoriKbnList.add("6"); // 教務卒業生
			}
		} else if ("GH".equals(productKbn)) {
			if ("GAK".equals(gakSotKbn)) {
				shoriKbnList.add("3"); // 学費在学生
				shoriKbnList.add("4"); // 学費出学予定者
			}
		}

		// 処理区分が設定できない場合は全区分で検索
		if (shoriKbnList.isEmpty()) {
			shoriKbnList.add("1");
			shoriKbnList.add("2");
			shoriKbnList.add("3");
			shoriKbnList.add("4");
			shoriKbnList.add("5");
		}

		// 学生検索条件を設定
		GhzGakSearchConditionDTO01 searchCondition = new GhzGakSearchConditionDTO01();
		searchCondition.setKanriNo(studentResult.getKanriNo());
		searchCondition.setShoriKbnList(shoriKbnList);
		searchCondition.setKijunDate(SystemInfo.getSystemDate());
		searchCondition.setTenkaIdoFlag(true);
		searchCondition.setNyugakYoteishaFlag(true);

		// 学生情報を検索
		return this.ghzGakSearchLogic.gakSotSerach(searchCondition);
	}

	/**
	 * 確認番号を生成
	 *
	 * @param payData 納付金データ
	 * @return 確認番号
	 */
	private String generateKakuninNo(final Xgm003DTO02 payData) {
		try {
			// 暫定実装として、固定値+納付金コードを使用
			String baseNo = "1" + payData.getPayCd();

			// 年度と納付金コードから連番を生成
			String sequenceNo = String.format("%04d",
				Math.abs((payData.getNendo() + payData.getPayCd().hashCode() +
				payData.getBunnoKbnCd() + payData.getBunkatsuNo()) % 10000));

			String kakuninNo = baseNo + sequenceNo;

			// 最大桁数制限
			if (kakuninNo.length() > 20) {
				kakuninNo = kakuninNo.substring(0, 20);
			}

			return kakuninNo;
		} catch (Exception e) {
			LOGGER.warn("確認番号生成エラー: " + e.getMessage());
			return ""; // エラー時は空文字を返す
		}
	}

	/**
	 * コンビニネットコードを生成
	 *
	 * @param payData 納付金データ
	 * @return コンビニネットコード
	 */
	private String generateKonbiniNetCd(final Xgm003DTO02 payData) {
		try {
			// バーコードを生成
			StringBuilder bcd = new StringBuilder();

			// 企業コード（暫定値）
			bcd.append("91234");

			// 委託コード（暫定値）
			bcd.append("567");

			// 顧客番号（確認番号の一部を使用）
			String kakuninNo = generateKakuninNo(payData);
			if (kakuninNo.length() >= 8) {
				bcd.append(kakuninNo.substring(0, 8));
			} else {
				bcd.append(String.format("%-8s", kakuninNo).replace(' ', '0'));
			}

			// 期限（YYYYMMDD形式）
			if (payData.getPayLimitDate() != null) {
				String limitDate = payData.getPayLimitDate().toString().replace("-", "");
				bcd.append(limitDate);
			} else {
				bcd.append("00000000");
			}

			// 金額（暫定値）
			bcd.append(String.format("%08d", 0));

			return bcd.toString();
		} catch (Exception e) {
			LOGGER.warn("コンビニネットコード生成エラー: " + e.getMessage());
			return ""; // エラー時は空文字を返す
		}
	}

	/**
	 * 振込依頼人コードを取得
	 *
	 * @param dbs DBセッション
	 * @param payData 納付金データ
	 * @param condition 非同期処理DTO
	 * @return 振込依頼人コード
	 */
	private String getFurikomiIraiCd(final DbSession dbs, final Xgm003DTO02 payData, final Xgm003CollectiveOutputDTO02 condition) {
		try {
			// 学籍番号リストから対象学籍番号を取得
			if (condition.getGaksekiCdList() == null || condition.getGaksekiCdList().isEmpty()) {
				return null;
			}

			// 学籍番号から管理番号を取得
			for (String gaksekiCd : condition.getGaksekiCdList()) {
				Long kanriNo = getKanriNoByGaksekiCd(dbs, gaksekiCd);
				if (kanriNo != null) {
					// 管理番号と納付金情報から振込依頼人コードを取得
					String furikomiIraiCd = getFurikomiIraiCdByPaymentInfo(dbs, kanriNo, payData);
					if (furikomiIraiCd != null) {
						return furikomiIraiCd;
					}
				}
			}

			return null;
		} catch (Exception e) {
			LOGGER.warn("振込依頼人コード取得エラー: " + e.getMessage());
			return null;
		}
	}

	/**
	 * 学籍番号から管理番号を取得
	 *
	 * @param dbs DBセッション
	 * @param gaksekiCd 学籍番号
	 * @return 管理番号
	 */
	private Long getKanriNoByGaksekiCd(final DbSession dbs, final String gaksekiCd) {
		try {
			// 学籍番号発番管理から管理番号を取得
			PkbGakHatbnDAO pkbGakHatbnDAO = dbs.getDao(PkbGakHatbnDAO.class);
			PkbGakHatbnAR pkbGakHatbnAR = pkbGakHatbnDAO.findByPrimaryKey(gaksekiCd);

			if (pkbGakHatbnAR != null) {
				return pkbGakHatbnAR.getKanriNo();
			}

			// 発番TBLに存在しない場合、学費学籍から取得
			GhbGakDAO ghbGakDAO = dbs.getDao(GhbGakDAO.class);
			List<GhbGakAR> ghbGakARList = ghbGakDAO.findByGaksekiCd(gaksekiCd);
			if (!ghbGakARList.isEmpty()) {
				return ghbGakARList.get(0).getKanriNo();
			}

			return null;
		} catch (Exception e) {
			LOGGER.warn("管理番号取得エラー: " + e.getMessage());
			return null;
		}
	}

	/**
	 * 管理番号と納付金情報から振込依頼人コードを取得
	 *
	 * @param dbs DBセッション
	 * @param kanriNo 管理番号
	 * @param payData 納付金データ
	 * @return 振込依頼人コード
	 */
	private String getFurikomiIraiCdByPaymentInfo(final DbSession dbs, final Long kanriNo, final Xgm003DTO02 payData) {
		try {
			// 納付金割当_分納DAOを取得
			GhePaywBunDAO ghePaywBunDAO = dbs.getDao(GhePaywBunDAO.class);

			// 管理番号、年度、納付金コード、パターンコード、分納区分コードで検索
			List<GhePaywBunAR> ghePaywBunARList = ghePaywBunDAO.findByNendoKanriNoPayPatternBunnoKbn(
					payData.getNendo(),
					kanriNo,
					payData.getPayCd(),
					payData.getPatternCd(),
					payData.getBunnoKbnCd()
			);

			// 該当するレコードから振込依頼人コードを取得
			for (GhePaywBunAR ghePaywBunAR : ghePaywBunARList) {
				if (ghePaywBunAR.getBunkatsuNo() == payData.getBunkatsuNo()) {
					return ghePaywBunAR.getFurikomiIraiCd();
				}
			}

			// 分割NOが一致しない場合は、最初のレコードの振込依頼人コードを返す
			if (!ghePaywBunARList.isEmpty()) {
				return ghePaywBunARList.get(0).getFurikomiIraiCd();
			}

			return null;
		} catch (Exception e) {
			LOGGER.warn("振込依頼人コード取得エラー: " + e.getMessage());
			return null;
		}
	}

}
