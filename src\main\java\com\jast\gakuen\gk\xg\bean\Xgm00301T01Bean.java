/*
 * Xgm00301T01Bean.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.bean;

import java.util.ArrayList;
import java.util.List;

import javax.enterprise.context.SessionScoped;
import javax.faces.model.SelectItem;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.context.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jast.gakuen.common.pk.constant.code.IdoShutgakApplyKekkaKbn;
import com.jast.gakuen.core.common.BaseSubBean;
import com.jast.gakuen.core.common.CodeManager;
import com.jast.gakuen.core.common.PaginatorManager;
import com.jast.gakuen.core.common.constant.AsyncExecConst;
import com.jast.gakuen.core.common.constant.AttrNameConst;
import com.jast.gakuen.core.common.constant.FileTypeConst;
import com.jast.gakuen.core.common.constant.Order;
import com.jast.gakuen.core.common.dto.CodeDTO;
import com.jast.gakuen.core.common.dto.OptionDTO;
import com.jast.gakuen.core.common.dto.OrderItemDTO;
import com.jast.gakuen.core.common.service.IOptionService;
import com.jast.gakuen.core.common.service.IOrdService;
import com.jast.gakuen.core.common.util.Transporter;
import com.jast.gakuen.core.common.util.UtilDialog;
import com.jast.gakuen.core.common.util.UtilFaces;
import com.jast.gakuen.core.common.util.UtilStr;
import com.jast.gakuen.core.gk.annotation.GkBackingBean;
import com.jast.gakuen.core.gk.annotation.GkWindowOpen;
import com.jast.gakuen.core.gk.bean.GkAsyncExecRequestBean;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO06;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO11;
import com.jast.gakuen.gk.pk.bean.PkzSelectItemManager;
import com.jast.gakuen.gk.pk.dto.Pka201DTO02;
import com.jast.gakuen.gk.pk.dto.Pkz205ConditionDTO01;
import com.jast.gakuen.gk.pk.dto.Pkz205DTO06;
import com.jast.gakuen.gk.pk.service.IPkzPrinterService;
import com.jast.gakuen.gk.xg.dto.Xgm003CollectiveOutputDTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO03;
import com.jast.gakuen.gk.xg.service.IXgm003Service;

import lombok.Getter;
import lombok.Setter;

/**
 * 学生納付金通知書(納付金指定)
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Named
@SessionScoped
public class Xgm00301T01Bean extends BaseSubBean {

	/**
	 * ログ出力
	 */
	private static final Logger logger = LoggerFactory.getLogger(Xgm00301T01Bean.class);

	/**
	 * 自由設定出力条件設定子画面ID
	 */
	protected static final String PKZ20501_FORM_ID = "Pkz20501";

	/**
	 * 学生納付金通知書ービス
	 */
	@Inject
	protected IXgm003Service xgm003Service;

	/**
	 * プリンタサービス
	 */
	@Inject
	protected IPkzPrinterService printerService;

	/**
	 * 学生納付金通知書出力
	 */
	@Inject
	@Getter
	protected Xgm00301Bean xgm00301Bean;

	/**
	 * 入出力項目並び順サービス
	 */
	@Inject
	protected IOrdService ordService;

	/**
	 * トランスポーター
	 */
	protected final Transporter transporter = new Transporter();

	/**
	 * 納付金指定 検索条件DTO
	 */
	@Getter
	protected final Xgm003ConditionDTO02 condition = new Xgm003ConditionDTO02();

	/**
	 * 並び順指定内容
	 */
	@Getter
	protected final Xgm003DTO03 t1_orderDetail = new Xgm003DTO03();

	/**
	 * 学生納付金通知書出力(データ)DTO(非同期処理)
	 */
	@Getter
	protected final Xgm003CollectiveOutputDTO02 collectiveOutputData = new Xgm003CollectiveOutputDTO02();

	/**
	 * コンボボックス項目を取得するサービス
	 */
	@Inject
	protected PkzSelectItemManager pkzSelectItemManager;

	/**
	 * 出身校名称
	 */
	@Getter
	protected String shshnkName;

	/**
	 * 異動種別
	 */
	@Getter
	protected List<SelectItem> idoShutgakSbtKbnList = new ArrayList<>();

	/**
	 * 納付金リスト
	 */
	@Getter
	protected List<Xgm003DTO02> payList = new ArrayList<>();

	/**
	 * 選択された納付金リスト
	 */
	@Getter
	@Setter
	protected List<Xgm003DTO02> selectedPayList = new ArrayList<>();

	/** 一覧行数 */
	@Getter
	protected Integer tblRows;

	/**
	 * 検索済みフラグ
	 */
	@Getter
	private boolean searched = false;

	/**
	 * 共通管轄学生
	 */
	@Getter
	@Setter
	private boolean gakDispFlg;
	/**
	 * 学費管轄学生
	 */
	@Getter
	@Setter
	private boolean ghbDispFlg;

	/**
	 * オプションサービス
	 */
	@Inject
	protected IOptionService optionService;

	/**
	 * 非同期処理実行登録Bean
	 */
	@Inject
	protected GkAsyncExecRequestBean gkAsyncExecRequestBean;

	/**
	 * 初期表示処理
	 *
	 * @throws Exception 例外
	 */
	public void doInit() throws Exception {


		// ------------------------------
		// コンボリスト生成
		// ------------------------------
		CodeManager manager = new CodeManager();
		CodeDTO codes = manager.getCode("PK0014");
		for (String code : codes.getItemValues()) {
			this.idoShutgakSbtKbnList.add(new SelectItem(code, codes.getLabel(code)));
		}

		// ------------------------------
		// 初期値設定
		// ------------------------------
		this.condition.setWariateNendo(this.xgm003Service.getCurrentNendo().getNendo());
		this.condition.setHakkoTgtZengakuMino(false);
		this.condition.setHakkoTgtIchibuMino(false);
		this.condition.getIdoShutgakApplyKekkaKbnList().add(IdoShutgakApplyKekkaKbn.Kyoka.getCode());

		// 納付金リスト
		this.getPayList().clear();
		this.getPayList().addAll(selectedPayList);

		// ※同一画面の別タブに並び順指定が読み込まれる場合、初期状態で２タブ目の並び順が表示されない問題を回避 START
		if (this.t1_orderDetail.getOrderItems().isEmpty()) {
			this.t1_orderDetail.getOrderItems().add(new OrderItemDTO());
			this.t1_orderDetail.getOrderItems().add(new OrderItemDTO());
			this.t1_orderDetail.getOrderItems().add(new OrderItemDTO());
		}
		// ※同一画面の別タブに並び順指定が読み込まれる場合、初期状態で２タブ目の並び順が表示されない問題を回避 END

		// ------------------------------
		// オプション情報を取得
		// ------------------------------
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition, this.t1_orderDetail);
		this.optionService.get(option);

		// 並び順のキーが未選択の場合も順序が保存されているので、キー未選択時は【昇順】に戻す
		for (OrderItemDTO order : t1_orderDetail.getOrderItems()) {
			if (UtilStr.isEmpty(order.getValue())) {
				order.setOrder(Order.ASC);
			}
		}

		// 前回表示行数をロード
		PaginatorManager.loadRowsPerPage();
		tblRows = UtilFaces.getRequestAttribute(AttrNameConst.RowsPerPage.getName());

	}

	/**
	 * 出身校検索画面の起動
	 * 
	 * @return 画面ID
	 * @throws Exception 例外
	 */
	@GkBackingBean
	@GkWindowOpen(other = "{\"openId\":\"Xgm00301T01:shshnkCdSearch\"}")
	public String doOpenSearchShshnk() throws Exception {
		return "Pka20101";
	}

	/**
	 * 出身校検索画面からの戻り値の受け取り処理
	 * 
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doGetShshnkCd() throws Exception {
		// 戻り値を受け取る（1件設定されている）
		List<Pka201DTO02> list = UtilDialog.getDialogParameters();
		Pka201DTO02 value = list.get(0);

		// 戻り値を設定
		this.condition.setShshnkCd(value.getShshnkCd());
		this.shshnkName = value.getShshnkName();
	}

	/**
	 * 出身校名称の取得
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doGetShshnkName() throws Exception {
		this.shshnkName = null;      // 初期化
		if (UtilStr.isEmpty(this.condition.getShshnkCd())) {
			return;
		}
		Ghd008DTO06 paramDto = new Ghd008DTO06();
		paramDto.setShshnkCd(condition.getShshnkCd());
		Ghd008DTO06 rtnDto = this.xgm003Service.getShshnkName(paramDto);
		if (rtnDto != null) {
			this.shshnkName = rtnDto.getShshnkName();
		}
	}

	/**
	 * 留学生フラグチェンジイベント
	 *
	 * @throws Exception 例外処理
	 */
	@GkBackingBean
	public void onChangeRyugakseiFlg() throws Exception {
		if (this.condition.getRyugakseiFlg() != null) {
			if (!this.condition.getRyugakseiFlg()) {
				this.condition.setRyugakseiKbn(null);
			}

		} else {
			this.condition.setRyugakseiKbn(null);
		}
	}

	/**
	 * 検索処理
	 *
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doSearch() throws Exception {

		// 納付金リストの初期化
		this.payList = new ArrayList<>();
		this.selectedPayList = new ArrayList<>();

		// 相関チェック
		// this.checkSearch();

		// 納付金リストの編集
		Xgm003ConditionDTO02 paramDto = new Xgm003ConditionDTO02();

		// 割当年度
		paramDto.setWariateNendo(this.condition.getWariateNendo());
		// 納付金種別
		paramDto.setNoufuKinShubetsu(this.condition.getNoufuKinShubetsu());
		// 業務コード
		paramDto.setGyomucd(this.condition.getGyomucd());

		List<Xgm003DTO02> payHList = this.xgm003Service.getPayHList(paramDto);

		this.payList = payHList;
		this.searched = true;
	}

	/**
	 * クリア処理
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doClear() throws Exception {
		// ------------------------------
		// 検索条件初期値設定
		// ------------------------------
		this.condition.setWariateNendo(this.xgm003Service.getCurrentNendo().getNendo());
		this.condition.setNoufuKinShubetsu(null);
		this.condition.setGyomucd(null);
	}

	/**
	 * 納付金通知書帳票出力の共通処理
	 *
	 * @param fileType 出力ファイル形式
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	public void outputReportProc(final FileTypeConst fileType, final Xgm003ConditionDTO02 conditionHeader) throws Exception {
		try {
			// 納付金通知書出力(帳票)DTO(非同期処理)に値をセットする
			this.setCollectiveOutput(this.collectiveOutputData);

			RequestContext.getCurrentInstance().addCallbackParam(AsyncExecConst.BeforeResultKey.getCode(), true);
		} catch (Exception e) {
			// エラーが発生した場合は、RequestContextにfalseを設定する
			RequestContext.getCurrentInstance().addCallbackParam(AsyncExecConst.BeforeResultKey.getCode(), false);
			throw e;
		}

		// オプション情報に保存
		saveOption();
	}

	/**
	 * 納付金通知書出力(帳票)DTO(非同期処理)に実行情報を設定する
	 *
	 * @param dto 非同期処理共通DTO
	 * @throws Exception 例外
	 */
	protected void setCollectiveOutput(final Xgm003CollectiveOutputDTO02 dto)
			throws Exception {
		// 納付金条件
		dto.setWariateNendo(this.condition.getWariateNendo());
		dto.setNoufuKinShubetsu(this.condition.getNoufuKinShubetsu());
		dto.setGyomucd(this.condition.getGyomucd());

		dto.setPayList(this.selectedPayList);

		

		// 教務管轄学生フラグ
		if (this.condition.getManageStuCommon()) {
			dto.setCommonManageStu(true);
		} else {
			dto.setCommonManageStu(false);
		}
		// 学費管轄学生フラグ
		if (this.condition.getManageStuTuition()) {
			dto.setGhbManageStu(true);
		} else {
			dto.setGhbManageStu(false);
		}
		if (this.condition.getManageStuList().isEmpty()) {
			dto.setCommonManageStu(true);
			dto.setGhbManageStu(true);
		}
		// 学生条件	
		dto.setNyugakNendo(this.condition.getNyugakNendo());
		dto.setNyugakGakkiNo(this.condition.getNyugakGakkiNo());
		dto.setGbushoCd(this.condition.getGbushoCd());
		dto.setSgksCd(this.condition.getSgksCd());
		dto.setGhSgksCd(this.condition.getGhSgksCd());
		dto.setNyugakNendoDeemed(this.condition.getNyugakNendoDeemed());
		dto.setNyugakGakkiNoDeemed(this.condition.getNyugakGakkiNoDeemed());
		dto.setCgksCd(this.condition.getCgksCd());
		dto.setGaknen(this.condition.getGaknen());
		dto.setSemester(this.condition.getSemester());
		dto.setNyugakSbtCd(this.condition.getNyugakSbtCd());
		dto.setShugakSbtCd(this.condition.getShugakSbtCd());
		dto.setMajorCourseSbtCd(this.condition.getMajorCourseSbtCd());
		dto.setMajorCourseCd(this.condition.getMajorCourseCd());
		dto.setClassSbtCd(this.condition.getClassSbtCd());
		dto.setClassCd(this.condition.getClassCd());
		dto.setRyugakseiFlg(this.condition.getRyugakseiFlg());
		dto.setRyugakseiKbn(this.condition.getRyugakseiKbn());
		dto.setShogaisyaFlg(this.condition.getShogaisyaFlg());
		dto.setShakaijinFlg(this.condition.getShakaijinFlg());
		dto.setPkGkfrList(this.condition.getPkGkfrList());
		dto.setGhGkfrList(this.condition.getGhGkfrList());
		dto.setShshnkCd(this.condition.getShshnkCd());
		dto.setIdoKikanFrom(this.condition.getIdoKikanFrom());
		dto.setIdoKikanTo(this.condition.getIdoKikanTo());
		dto.setIdoShutgakApplyKekkaKbnList(this.condition.getIdoShutgakApplyKekkaKbnList());
		dto.setIdoShutgakSbtKbnList(this.condition.getIdoShutgakSbtKbnList());

		dto.setOrderItems(t1_orderDetail.getOrderItems());
	}

	/**
	 * オプション情報に保存
	 *
	 * @throws Exception 例外
	 */
	protected void saveOption() throws Exception {
		this.xgm00301Bean.saveOption();

		// オプション情報を保存
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition, this.t1_orderDetail);
		this.optionService.set(option);
	}

	// /**
	//  * 相関チェック
	//  *
	//  * @throws Exception 例外
	//  */
	// protected void checkProc() throws Exception {

	// 	// 納付金一覧が1件も選択されていなければエラー
	// 	if (this.selectedPayList.isEmpty()) {
	// 		throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 40,
	// 				UtilLocalization.getItemValue("common.pay.0.label")));
	// 	}

	// 	// // 納入開始日TO < 納入開始日FROM であればエラー
	// 	// if (condition.getPayStartDateFrom() != null && condition.getPayStartDateTo() != null) {
	// 	// 	if (condition.getPayStartDateFrom().after(condition.getPayStartDateTo())) {
	// 	// 		throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 56,
	// 	// 				UtilLocalization.getItemValue("ghd008.payStartDateFrom.0.label"),
	// 	// 				UtilLocalization.getItemValue("ghd008.payStartDateTo.0.label")));
	// 	// 	}
	// 	// }

	// 	// // 納入期限日TO < 納入期限日FROM であればエラー
	// 	// if (condition.getPayLimitDateFrom() != null && condition.getPayLimitDateTo() != null) {
	// 	// 	if (condition.getPayLimitDateFrom().after(condition.getPayLimitDateTo())) {
	// 	// 		throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 56,
	// 	// 				UtilLocalization.getItemValue("ghd008.payLimitDateFrom.0.label"),
	// 	// 				UtilLocalization.getItemValue("ghd008.payLimitDateTo.0.label")));
	// 	// 	}
	// 	// }

	// 	// 入学年度が未入力、入学学期ＮＯが入力の場合、エラー
	// 	if (this.condition.getNyugakNendo() == null && this.condition.getNyugakGakkiNo() != null) {
	// 		throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 154,
	// 				UtilLocalization.getItemValue("common.nyugakGakkiNo.0.label"),
	// 				UtilLocalization.getItemValue("common.nyugakNendo.0.label")));
	// 	}

	// 	// みなし入学年度が未入力、みなし入学学期ＮＯが入力の場合、エラー
	// 	if (this.condition.getNyugakNendoDeemed() == null && this.condition.getNyugakGakkiNoDeemed() != null) {
	// 		throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 154,
	// 				UtilLocalization.getItemValue("pkbGakIdo.nyugakGakkiNoDeemed.0.label"),
	// 				UtilLocalization.getItemValue("pkbGakIdo.nyugakNendoDeemed.0.label")));
	// 	}

	// 	// 異動期間FROM < 異動期間TO であればエラー
	// 	if (condition.getIdoKikanFrom() != null && condition.getIdoKikanTo() != null) {
	// 		if (condition.getIdoKikanFrom().after(condition.getIdoKikanTo())) {
	// 			throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 56,
	// 					UtilLocalization.getItemValue("common.yidoKikanFrom.0.label"),
	// 					UtilLocalization.getItemValue("common.yidoKikanTo.0.label")));
	// 		}
	// 	}

	// 	// 同一の「項目」が選択されている場合エラー
	// 	PkzOrderCheckUtil pkzOrderCheckUtil = new PkzOrderCheckUtil(t1_orderDetail);
	// 	PkzOrderCheckDTO sameKomokResult = pkzOrderCheckUtil.checkSameKomok();
	// 	if (sameKomokResult.getMessage() != null) {
	// 		throw new DataException(sameKomokResult.getMessage());
	// 	}
	// }

	/**
	 * 「学生自由設定 編集」ボタン押下
	 * 
	 * @return 子画面ＩＤ
	 * @throws Exception 例外
	 */
	@GkBackingBean
	@GkWindowOpen(other = "{\"openId\":\"Xgm00301T01:doClickPkGkfrCondition\"}")
	public String doClickPkGkfrCondition() throws Exception {
		// パラメータを生成する
		Pkz205ConditionDTO01 params = new Pkz205ConditionDTO01();
		params.setFreeSetKbn("1");

		for (Ghd008DTO11 frDto : this.condition.getPkGkfrList()) {
			// 自由設定項目情報
			Pkz205DTO06 dto = new Pkz205DTO06();
			dto.setFreeTgtCd(frDto.getFreeTgtCd());
			dto.setFreeKomokNo(frDto.getFreeKomokNo());
			dto.setFreeVal(frDto.getFreeVal());
			params.getFreeSetList().add(dto);
		}

		UtilDialog.setDialogParameters(params);
		return PKZ20501_FORM_ID;
	}

	/**
	 * 「学生自由設定 編集」戻り値取得
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doReceivePkGkfrCondition() throws Exception {

		// 画面間連係情報を取得
		List<Pkz205DTO06> rtnList = UtilDialog.getDialogParameters();
		if (rtnList != null) {

			List<Ghd008DTO11> dtoList = new ArrayList<>();

			for (Pkz205DTO06 rtnDto : rtnList) {
				Ghd008DTO11 dto = new Ghd008DTO11();
				dto.setFreeTgtCd(rtnDto.getFreeTgtCd());
				dto.setFreeKomokNo(rtnDto.getFreeKomokNo());
				dto.setFreeVal(rtnDto.getFreeVal());
				dtoList.add(dto);
			}
			this.condition.setPkGkfrList(dtoList);
		}
	}

	/**
	 * 「学費学生自由設定 編集」ボタン押下
	 * 
	 * @return 子画面ＩＤ
	 * @throws Exception 例外
	 */
	@GkBackingBean
	@GkWindowOpen(other = "{\"openId\":\"Xgm00301T01:doClickGhGkfrCondition\"}")
	public String doClickGhGkfrCondition() throws Exception {
		// パラメータを生成する
		Pkz205ConditionDTO01 params = new Pkz205ConditionDTO01();
		params.setFreeSetKbn("5");

		for (Ghd008DTO11 frDto : this.condition.getGhGkfrList()) {
			// 自由設定項目情報
			Pkz205DTO06 dto = new Pkz205DTO06();
			dto.setFreeTgtCd(frDto.getFreeTgtCd());
			dto.setFreeKomokNo(frDto.getFreeKomokNo());
			dto.setFreeVal(frDto.getFreeVal());
			params.getFreeSetList().add(dto);
		}

		UtilDialog.setDialogParameters(params);
		return PKZ20501_FORM_ID;
	}

	/**
	 * 「学費学生自由設定 編集」戻り値取得
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doReceiveGhGkfrCondition() throws Exception {

		// 画面間連係情報を取得
		List<Pkz205DTO06> rtnList = UtilDialog.getDialogParameters();
		if (rtnList != null) {

			List<Ghd008DTO11> dtoList = new ArrayList<>();

			for (Pkz205DTO06 rtnDto : rtnList) {
				Ghd008DTO11 dto = new Ghd008DTO11();
				dto.setFreeTgtCd(rtnDto.getFreeTgtCd());
				dto.setFreeKomokNo(rtnDto.getFreeKomokNo());
				dto.setFreeVal(rtnDto.getFreeVal());
				dtoList.add(dto);
			}
			this.condition.setGhGkfrList(dtoList);
		}
	}
}
