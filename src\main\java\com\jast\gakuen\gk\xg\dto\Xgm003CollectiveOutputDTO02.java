/*
 * Xgm003CollectiveOutputDTO02.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.faces.model.SelectItem;

import com.jast.gakuen.core.common.annotation.RxOpt;
import com.jast.gakuen.core.common.annotation.RxValidationForUI;
import com.jast.gakuen.core.common.dto.OrderItemDTO;
import com.jast.gakuen.core.common.util.GakuenProperty;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO11;
import com.jast.gakuen.gk.xg.async.Xgm003Async;

import lombok.Getter;
import lombok.Setter;

/**
 * 学生納付金通知書DTO(非同期処理)
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Getter
@Setter
public class Xgm003CollectiveOutputDTO02 extends Xgm003CollectiveOutputDTO01 {

	/**
	 * コンストラクタ
	 */
	public Xgm003CollectiveOutputDTO02() {
		// プロダクトコード
		setPrdCd(Xgm003Async.PRD_CD);
		// 非同期処理ＩＤ
		setAsyncExecId(Xgm003Async.ASYNC_EXEC_ID);
		// 処理コード
		setExecCd(Xgm003Async.EXEC_PACKAGE_OUTPUT);
		// 非同期処理クラス
		setAsyncExecClass(Xgm003Async.class);
		// チェックのみフラグ
		setCheckOnly(false);
		// 処理続行確認フラグ
		setContinueConfirm(true);
		// 言語コード
		setLangCd(GakuenProperty.getLangCd());
	}

	/**
	 * 割当年度
	 */
	protected Integer wariateNendo;

	/**
	 * 納付金種別
	 */
	private List<String> noufuKinShubetsu;

	/**
	 * 業務コード
	 */
	private String gyomucd;

	/**
	 * 発行対象状況区分（全額未納）
	 */
	protected boolean hakkoTgtZengakuMino;

	/**
	 * 発行対象状況区分（一部）
	 */
	protected boolean hakkoTgtIchibuMino;

	/**
	 * 共通直轄学生
	 */
	protected boolean commonManageStu;

	/**
	 * 学費直轄学生
	 */
	protected boolean ghbManageStu;

	/**
	 * 入学年度
	 */
	protected Integer nyugakNendo;

	/**
	 * 入学学期NO
	 */
	protected Integer nyugakGakkiNo;

	/**
	 * 学生管理部署
	 */
	protected String gbushoCd;

	/**
	 * 所属学科組織
	 */
	protected String sgksCd;

	/**
	 * 学費所属学科組織
	 */
	protected String ghSgksCd;

	/**
	 * みなし入学年度
	 */
	protected Integer nyugakNendoDeemed;

	/**
	 * みなし入学学期NO
	 */
	protected Integer nyugakGakkiNoDeemed;

	/**
	 * カリキュラム学科組織
	 */
	protected String cgksCd;

	/**
	 * 学年
	 */
	protected Integer gaknen;

	/**
	 * セメスタ
	 */
	protected Integer semester;

	/**
	 * 入学種別
	 */
	protected String nyugakSbtCd;

	/**
	 * 就学種別
	 */
	protected String shugakSbtCd;

	/**
	 * 専攻コース種別
	 */
	protected String majorCourseSbtCd;

	/**
	 * 専攻コース
	 */
	protected String majorCourseCd;

	/**
	 * クラス種別
	 */
	protected String classSbtCd;

	/**
	 * クラス
	 */
	protected String classCd;

	/**
	 * 留学生対象
	 */
	protected Boolean ryugakseiFlg;

	/**
	 * 留学生区分
	 */
	protected String ryugakseiKbn;

	/**
	 * 障害者対象
	 */
	protected Boolean shogaisyaFlg;

	/**
	 * 社会人対象
	 */
	protected Boolean shakaijinFlg;

	/**
	 * 共通学生自由設定項目リスト
	 */
	protected List<Ghd008DTO11> pkGkfrList = new ArrayList<>();

	/**
	 * 共通卒業生自由設定項目リスト
	 */
	protected List<Ghd008DTO11> pkStfrList = new ArrayList<>();

	/**
	 * 学費学生自由設定項目リスト
	 */
	protected List<Ghd008DTO11> ghGkfrList = new ArrayList<>();

	/**
	 * 学費卒業生自由設定項目リスト
	 */
	protected List<Ghd008DTO11> ghStfrList = new ArrayList<>();

	/**
	 * 出身校コード
	 */
	protected String shshnkCd;

	/**
	 * 異動期間FROM
	 */
	protected Date idoKikanFrom;

	/**
	 * 異動期間TO
	 */
	protected Date idoKikanTo;

	/**
	 * 異動出学申請結果区分リスト
	 */
	protected List<String> idoShutgakApplyKekkaKbnList = new ArrayList<>();

	/**
	 * 異動出学種別区分リスト
	 */
	protected List<String> idoShutgakSbtKbnList = new ArrayList<>();

	/**
	 * 学籍番号リスト
	 */
	protected List<String> gaksekiCdList = new ArrayList<>();

	/**
	 * 並び順指定
	 */
	protected List<OrderItemDTO> orderItems = new ArrayList<>();

	/**
	 * 納付金リスト
	 */
	private List<Xgm003DTO02> payList = new ArrayList<>();
}
